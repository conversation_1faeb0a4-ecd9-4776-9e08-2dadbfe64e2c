<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تبدیل تصویر به آیکون‌های اکستنشن</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON>, <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .input-section {
            margin-bottom: 20px;
        }
        .preview-section {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .preview-item {
            text-align: center;
        }
        .preview-item canvas {
            border: 1px solid #ddd;
            margin-bottom: 5px;
            background-color: white;
        }
        button {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 10px;
        }
        button:hover {
            background-color: #3367d6;
        }
        .download-links {
            margin-top: 20px;
        }
        .download-links a {
            display: block;
            margin-bottom: 8px;
            color: #4285f4;
            text-decoration: none;
        }
        .download-links a:hover {
            text-decoration: underline;
        }
        .instructions {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-right: 4px solid #4285f4;
        }
        #drop-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            transition: background-color 0.3s;
        }
        #drop-area.highlight {
            background-color: #e8f0fe;
            border-color: #4285f4;
        }
        #file-input {
            display: none;
        }
        .file-label {
            display: inline-block;
            background-color: #4285f4;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        .file-label:hover {
            background-color: #3367d6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>تبدیل تصویر به آیکون‌های اکستنشن</h1>

        <div class="instructions">
            <h2>راهنما</h2>
            <p>با استفاده از این ابزار می‌توانید تصویر مورد نظر خود را به آیکون‌های مختلف برای استفاده در اکستنشن تبدیل کنید.</p>
            <p>اندازه‌های مورد نیاز برای آیکون‌های اکستنشن:</p>
            <ul>
                <li>16×16 پیکسل</li>
                <li>32×32 پیکسل</li>
                <li>48×48 پیکسل</li>
                <li>64×64 پیکسل</li>
                <li>96×96 پیکسل</li>
                <li>128×128 پیکسل</li>
            </ul>
        </div>

        <div class="input-section">
            <div id="drop-area">
                <p>تصویر خود را اینجا بکشید و رها کنید</p>
                <p>یا</p>
                <label for="file-input" class="file-label">انتخاب تصویر</label>
                <input type="file" id="file-input" accept="image/*">
            </div>

            <div id="source-image-container" style="display: none;">
                <h3>تصویر اصلی:</h3>
                <img id="source-image" style="max-width: 300px; max-height: 300px;">
                <button id="convert-btn">تبدیل به آیکون‌ها</button>
            </div>
        </div>

        <div id="preview-container" style="display: none;">
            <h2>پیش‌نمایش آیکون‌ها</h2>
            <div class="preview-section" id="preview-section"></div>

            <div class="download-links" id="download-links"></div>
        </div>
    </div>

    <!-- Load the external script file -->
    <script src="convert-svg-to-png.js"></script>
</body>
</html>
