// تنظیم ناحیه کشیدن و رها کردن
document.addEventListener('DOMContentLoaded', function() {
    const dropArea = document.getElementById('drop-area');
    const fileInput = document.getElementById('file-input');
    const sourceImage = document.getElementById('source-image');
    const sourceImageContainer = document.getElementById('source-image-container');
    const convertBtn = document.getElementById('convert-btn');
    const previewContainer = document.getElementById('preview-container');
    const previewSection = document.getElementById('preview-section');
    const downloadLinks = document.getElementById('download-links');

    // اندازه‌های آیکون
    const iconSizes = [16, 32, 48, 64, 96, 128];

    // جلوگیری از رفتار پیش‌فرض مرورگر برای کشیدن و رها کردن
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // اضافه کردن کلاس highlight هنگام کشیدن
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight() {
        dropArea.classList.add('highlight');
    }

    function unhighlight() {
        dropArea.classList.remove('highlight');
    }

    // پردازش فایل رها شده
    dropArea.addEventListener('drop', handleDrop, false);
    fileInput.addEventListener('change', handleFiles, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles({ target: { files } });
    }

    function handleFiles(e) {
        const file = e.target.files[0];
        if (file && file.type.match('image.*')) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                sourceImage.src = e.target.result;
                sourceImageContainer.style.display = 'block';
                previewContainer.style.display = 'none';
                previewSection.innerHTML = '';
                downloadLinks.innerHTML = '';
            };
            
            reader.readAsDataURL(file);
        }
    }

    // تبدیل تصویر به آیکون‌ها
    convertBtn.addEventListener('click', convertToIcons);

    function convertToIcons() {
        previewSection.innerHTML = '';
        downloadLinks.innerHTML = '';
        
        iconSizes.forEach(size => {
            createIcon(size);
        });
        
        previewContainer.style.display = 'block';
    }

    function createIcon(size) {
        const canvas = document.createElement('canvas');
        canvas.width = size;
        canvas.height = size;
        const ctx = canvas.getContext('2d');
        
        // کشیدن تصویر در canvas با اندازه مشخص شده
        ctx.drawImage(sourceImage, 0, 0, size, size);
        
        // ایجاد پیش‌نمایش
        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';
        
        const previewCanvas = document.createElement('canvas');
        previewCanvas.width = size;
        previewCanvas.height = size;
        const previewCtx = previewCanvas.getContext('2d');
        previewCtx.drawImage(canvas, 0, 0);
        
        const sizeLabel = document.createElement('div');
        sizeLabel.textContent = `${size}×${size}`;
        
        previewItem.appendChild(previewCanvas);
        previewItem.appendChild(sizeLabel);
        previewSection.appendChild(previewItem);
        
        // ایجاد لینک دانلود
        canvas.toBlob(function(blob) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `icon-${size}.png`;
            link.textContent = `دانلود آیکون ${size}×${size}`;
            downloadLinks.appendChild(link);
        });
    }
});
